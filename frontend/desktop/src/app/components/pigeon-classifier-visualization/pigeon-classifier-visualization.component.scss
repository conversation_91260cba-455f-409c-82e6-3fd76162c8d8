.pigeon-classifier-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.stats-card {
  transition: transform 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  mat-card-content {
    padding: 1.5rem;
  }
}

.pigeon-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
  
  img {
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  .mat-mdc-card-content {
    padding: 0;
  }
}

// Custom chip styling
.mat-mdc-chip {
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 16px;
  
  &.character-chip {
    color: white !important;
  }
  
  &.distinctiveness-chip {
    color: white !important;
  }
}

// Filter section styling
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    border-radius: 8px;
  }
}

// Grid responsive adjustments
@media (max-width: 768px) {
  .pigeon-classifier-container {
    padding: 1rem;
  }
  
  .grid {
    gap: 1rem;
  }
  
  .pigeon-card {
    img {
      height: 200px;
    }
  }
}

@media (max-width: 640px) {
  .stats-card {
    mat-card-content {
      padding: 1rem;
    }
  }
  
  .pigeon-card {
    img {
      height: 180px;
    }
  }
}

// Loading state
.mat-mdc-progress-spinner {
  margin: 2rem auto;
}

// Badge positioning
.absolute {
  &.top-2 {
    &.right-2 {
      z-index: 10;
    }
    
    &.left-2 {
      z-index: 10;
    }
  }
}

// Trait tags styling
.trait-tag {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  margin: 0.125rem;
  
  &.base-color {
    background-color: #dbeafe;
    color: #1e40af;
  }
  
  &.pattern {
    background-color: #dcfce7;
    color: #166534;
  }
  
  &.piebald {
    background-color: #f3e8ff;
    color: #7c3aed;
  }
  
  &.spread {
    background-color: #f3f4f6;
    color: #374151;
  }
}

// Character color variations
.character-blue { background-color: #3b82f6 !important; }
.character-brown { background-color: #a16207 !important; }
.character-red { background-color: #dc2626 !important; }
.character-gray { background-color: #6b7280 !important; }
.character-purple { background-color: #7c3aed !important; }
.character-green { background-color: #059669 !important; }
.character-orange { background-color: #ea580c !important; }
.character-indigo { background-color: #4f46e5 !important; }

// Distinctiveness color variations
.distinctiveness-common { background-color: #059669 !important; }
.distinctiveness-unusual { background-color: #ea580c !important; }
.distinctiveness-rare { background-color: #dc2626 !important; }

// Animation for card entrance
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pigeon-card {
  animation: fadeInUp 0.5s ease-out;
  animation-fill-mode: both;
  
  @for $i from 1 through 20 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.05}s;
    }
  }
}

// No results state
.no-results {
  .mat-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
  }
}

// Scrollbar styling
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;

  &:hover {
    background: #a8a8a8;
  }
}

// Hover panel styling
.hover-panel {
  animation: fadeInScale 0.2s ease-out;
  backdrop-filter: blur(8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  img {
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.02);
    }
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Enhanced pigeon image hover effects
.pigeon-image {
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Trait badges styling
.trait-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;

  &.species {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.distinctiveness {
    &.common {
      background-color: #dcfce7;
      color: #166534;
    }

    &.unusual {
      background-color: #fed7aa;
      color: #c2410c;
    }

    &.rare {
      background-color: #fecaca;
      color: #dc2626;
    }
  }

  &.baby {
    background-color: #fef3c7;
    color: #d97706;
  }

  &.dead {
    background-color: #fee2e2;
    color: #dc2626;
  }
}
