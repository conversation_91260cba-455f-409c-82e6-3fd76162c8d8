import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { PigeonClassifierVisualizationComponent } from './pigeon-classifier-visualization.component';
import { PigeonClassifierService, ClassifiedPigeon, ClassificationReport } from '../../services/pigeon-classifier.service';
import { of } from 'rxjs';

describe('PigeonClassifierVisualizationComponent', () => {
  let component: PigeonClassifierVisualizationComponent;
  let fixture: ComponentFixture<PigeonClassifierVisualizationComponent>;
  let mockPigeonClassifierService: jasmine.SpyObj<PigeonClassifierService>;

  const mockPigeon: ClassifiedPigeon = {
    filename: 'test-pigeon.jpg',
    character: 'BLUE_BAR',
    description: 'A beautiful blue bar pigeon',
    imageUrl: 'assets/test-pigeon.jpg',
    analysis: {
      filename: 'test-pigeon.jpg',
      looks_like_a_screenshot: 0.1,
      is_bird: true,
      species: 'pigeon',
      is_baby_pigeon: false,
      is_dead: false,
      distinctiveness: 'common',
      description: 'A typical blue bar pigeon with standard markings',
      pigeon_traits: {
        base_color: 'blue',
        main_pattern: 'bar',
        is_spread: false,
        spread_level: 'none',
        is_piebald: false,
        piebald_level: 'none',
        piebald_pattern: 'none',
        piebald_intensity: null,
        head_pattern: 'none',
        iridescence_level: 'medium',
        wing_tip_color: 'black',
        tail_color: 'dark',
        face_pattern: 'uniform_standard'
      }
    }
  };

  const mockClassificationReport: ClassificationReport = {
    total_images: 1,
    total_unique_characters: 1,
    target_characters: 1,
    character_distribution: { 'BLUE_BAR': 1 },
    examples: [mockPigeon]
  };

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('PigeonClassifierService', [
      'loadClassificationData',
      'getClassifiedPigeons',
      'getCharacterDistribution',
      'getUniqueCharacters',
      'getUniqueDistinctiveness',
      'getCharacterDisplayName',
      'getCharacterColor'
    ]);

    await TestBed.configureTestingModule({
      imports: [
        PigeonClassifierVisualizationComponent,
        HttpClientTestingModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: PigeonClassifierService, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(PigeonClassifierVisualizationComponent);
    component = fixture.componentInstance;
    mockPigeonClassifierService = TestBed.inject(PigeonClassifierService) as jasmine.SpyObj<PigeonClassifierService>;

    // Setup mock service responses
    mockPigeonClassifierService.loadClassificationData.and.returnValue(of(mockClassificationReport));
    mockPigeonClassifierService.getClassifiedPigeons.and.returnValue(of([mockPigeon]));
    mockPigeonClassifierService.getCharacterDistribution.and.returnValue(of({ 'BLUE_BAR': 1 }));
    mockPigeonClassifierService.getUniqueCharacters.and.returnValue(of(['BLUE_BAR']));
    mockPigeonClassifierService.getUniqueDistinctiveness.and.returnValue(of(['common']));
    mockPigeonClassifierService.getCharacterDisplayName.and.returnValue('Blue Bar');
    mockPigeonClassifierService.getCharacterColor.and.returnValue('blue');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should show hover panel when pigeon is hovered', () => {
    fixture.detectChanges();
    
    const mockEvent = new MouseEvent('mouseenter', {
      clientX: 100,
      clientY: 100
    });

    component.onPigeonHover(mockPigeon, mockEvent);

    expect(component.hoveredPigeon()).toEqual(mockPigeon);
    expect(component.showHoverPanel()).toBe(true);
    expect(component.hoverPanelPosition().x).toBeGreaterThan(0);
    expect(component.hoverPanelPosition().y).toBeGreaterThan(0);
  });

  it('should hide hover panel when pigeon hover ends', () => {
    fixture.detectChanges();
    
    // First show the panel
    const mockEvent = new MouseEvent('mouseenter', {
      clientX: 100,
      clientY: 100
    });
    component.onPigeonHover(mockPigeon, mockEvent);
    
    expect(component.showHoverPanel()).toBe(true);

    // Then hide it
    component.onPigeonLeave();
    
    expect(component.showHoverPanel()).toBe(false);
  });

  it('should position hover panel correctly to avoid screen edges', () => {
    fixture.detectChanges();
    
    // Mock window dimensions
    spyOnProperty(window, 'innerWidth').and.returnValue(800);
    spyOnProperty(window, 'innerHeight').and.returnValue(600);

    // Test positioning near right edge
    const mockEventNearRightEdge = new MouseEvent('mouseenter', {
      clientX: 750, // Near right edge
      clientY: 100
    });

    component.onPigeonHover(mockPigeon, mockEventNearRightEdge);

    // Panel should be positioned to the left of cursor
    expect(component.hoverPanelPosition().x).toBeLessThan(750);
  });

  it('should display pigeon traits correctly in hover panel', () => {
    fixture.detectChanges();
    component.hoveredPigeon.set(mockPigeon);
    component.showHoverPanel.set(true);
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;
    
    // Check if traits are displayed
    expect(compiled.textContent).toContain('blue'); // base_color
    expect(compiled.textContent).toContain('bar'); // main_pattern
    expect(compiled.textContent).toContain('medium'); // iridescence_level
  });
});
