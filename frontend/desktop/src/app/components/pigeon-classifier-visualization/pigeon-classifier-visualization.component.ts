import { Component, OnInit, OnD<PERSON>roy, ChangeDetectionStrategy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Subject, takeUntil, combineLatest, debounceTime, distinctUntilChanged, of } from 'rxjs';

import { PigeonClassifierService, ClassifiedPigeon, CharacterDistribution } from '../../services/pigeon-classifier.service';

@Component({
  selector: 'app-pigeon-classifier-visualization',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatChipsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatBadgeModule,
    MatTooltipModule
  ],
  templateUrl: './pigeon-classifier-visualization.component.html',
  styleUrls: ['./pigeon-classifier-visualization.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PigeonClassifierVisualizationComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Signals for reactive state management
  allPigeons = signal<ClassifiedPigeon[]>([]);
  filteredPigeons = signal<ClassifiedPigeon[]>([]);
  characterDistribution = signal<CharacterDistribution>({});
  uniqueCharacters = signal<string[]>([]);
  uniqueDistinctiveness = signal<string[]>([]);
  loading = signal<boolean>(false);

  // Filter controls
  selectedCharacter = signal<string>('');
  selectedDistinctiveness = signal<string>('');
  searchQuery = signal<string>('');
  sortBy = signal<'character' | 'distinctiveness' | 'filename'>('character');

  // Hover panel state
  hoveredPigeon = signal<ClassifiedPigeon | null>(null);
  showHoverPanel = signal<boolean>(false);
  hoverPanelPosition = signal<{ x: number; y: number }>({ x: 0, y: 0 });

  // Computed values
  totalPigeons = computed(() => this.allPigeons().length);
  totalCharacters = computed(() => this.uniqueCharacters().length);
  filteredCount = computed(() => this.filteredPigeons().length);

  // Character groups for display
  characterGroups = computed(() => {
    const groups: { character: string; pigeons: ClassifiedPigeon[] }[] = [];
    const pigeonsByCharacter = new Map<string, ClassifiedPigeon[]>();

    // Group pigeons by character
    this.allPigeons().forEach(pigeon => {
      if (!pigeonsByCharacter.has(pigeon.character)) {
        pigeonsByCharacter.set(pigeon.character, []);
      }
      pigeonsByCharacter.get(pigeon.character)!.push(pigeon);
    });

    // Convert to array and sort by character name
    Array.from(pigeonsByCharacter.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([character, pigeons]) => {
        groups.push({ character, pigeons });
      });

    return groups;
  });

  constructor(private pigeonClassifierService: PigeonClassifierService) {}

  ngOnInit(): void {
    this.loadData();
    this.setupFiltering();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadData(): void {
    this.loading.set(true);

    // Load initial data first
    this.pigeonClassifierService.loadClassificationData().pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      // Then load all the individual data streams
      this.pigeonClassifierService.getClassifiedPigeons().pipe(
        takeUntil(this.destroy$)
      ).subscribe(pigeons => {
        this.allPigeons.set(pigeons);
        this.applyFilters();
      });

      this.pigeonClassifierService.getCharacterDistribution().pipe(
        takeUntil(this.destroy$)
      ).subscribe(distribution => {
        this.characterDistribution.set(distribution);
      });

      this.pigeonClassifierService.getUniqueCharacters().pipe(
        takeUntil(this.destroy$)
      ).subscribe(characters => {
        this.uniqueCharacters.set(characters);
      });

      this.pigeonClassifierService.getUniqueDistinctiveness().pipe(
        takeUntil(this.destroy$)
      ).subscribe(distinctiveness => {
        this.uniqueDistinctiveness.set(distinctiveness);
      });

      this.loading.set(false);
    });
  }

  private setupFiltering(): void {
    // React to filter changes using effect or manual subscription
    // For now, we'll handle filtering in the methods directly
    // since signals don't have asObservable() method
  }

  private applyFilters(): void {
    let filtered = [...this.allPigeons()];

    // Apply character filter
    if (this.selectedCharacter()) {
      filtered = filtered.filter(p => p.character === this.selectedCharacter());
    }

    // Apply distinctiveness filter
    if (this.selectedDistinctiveness()) {
      filtered = filtered.filter(p => p.analysis.distinctiveness === this.selectedDistinctiveness());
    }

    // Apply search filter
    if (this.searchQuery()) {
      const query = this.searchQuery().toLowerCase();
      filtered = filtered.filter(p =>
        p.filename.toLowerCase().includes(query) ||
        p.description.toLowerCase().includes(query) ||
        p.analysis.description.toLowerCase().includes(query) ||
        p.character.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (this.sortBy()) {
        case 'character':
          return a.character.localeCompare(b.character);
        case 'distinctiveness':
          return a.analysis.distinctiveness.localeCompare(b.analysis.distinctiveness);
        case 'filename':
          return a.filename.localeCompare(b.filename);
        default:
          return 0;
      }
    });

    this.filteredPigeons.set(filtered);
  }

  onCharacterFilterChange(character: string): void {
    this.selectedCharacter.set(character);
    this.applyFilters();
  }

  onDistinctivenessFilterChange(distinctiveness: string): void {
    this.selectedDistinctiveness.set(distinctiveness);
    this.applyFilters();
  }

  onSearchQueryChange(query: string): void {
    this.searchQuery.set(query);
    this.applyFilters();
  }

  onSortChange(sortBy: 'character' | 'distinctiveness' | 'filename'): void {
    this.sortBy.set(sortBy);
    this.applyFilters();
  }

  clearFilters(): void {
    this.selectedCharacter.set('');
    this.selectedDistinctiveness.set('');
    this.searchQuery.set('');
    this.applyFilters();
  }

  getCharacterDisplayName(character: string): string {
    return this.pigeonClassifierService.getCharacterDisplayName(character);
  }

  getCharacterColor(character: string): string {
    return this.pigeonClassifierService.getCharacterColor(character);
  }

  getCharacterColorHex(character: string): string {
    const colorMap: { [key: string]: string } = {
      'BLUE_BAR': '#3B82F6',
      'BLUE_CHECKER': '#1E40AF',
      'BLUE_T_CHECK': '#1D4ED8',
      'BLUE_BARLESS': '#60A5FA',
      'BROWN_BAR': '#A16207',
      'BROWN_CHECKER': '#92400E',
      'BROWN_OTHER': '#B45309',
      'BROWN_BARLESS': '#D97706',
      'ASH_RED_BAR': '#DC2626',
      'ASH_RED_CHECKER': '#B91C1C',
      'ASH_RED_BARLESS': '#EF4444',
      'BLUE_SPREAD_BAR': '#1F2937',
      'BLUE_SPREAD_CHECKER': '#374151',
      'BLUE_SPREAD_T_CHECK': '#4B5563',
      'BLUE_SPREAD_OTHER': '#6B7280',
      'HELMET_PIGEON': '#7C3AED',
      'BALDHEAD_PIGEON': '#8B5CF6',
      'MOTTLED_FACE': '#F59E0B',
      'WEIRD_FACE_SPOTS': '#F97316',
      'HIGH_IRIDESCENCE': '#10B981',
      'NO_IRIDESCENCE': '#6B7280',
      'WHITE_WING_TIPS': '#E5E7EB',
      'WHITE_TAIL': '#F3F4F6',
      'MIXED_TAIL_COLOR': '#9CA3AF',
      'OTHER_BASE_COLOR': '#8B5A2B',
      'RARE_DISTINCTIVENESS': '#EC4899',
      'MAGPIE': '#000000',
      'WOOD_PIGEON': '#059669',
      'TURTLEDOVE': '#0891B2',
      'DUCK': '#0284C7',
      'CROW': '#111827',
      'RAVEN': '#030712',
      'SEAGULL': '#F8FAFC',
      'ROOSTER': '#DC2626',
      'OTHER_BIRD': '#6B7280',
      'BABY_PIGEON': '#FDE047',
      'DEAD_PIGEON': '#7F1D1D',
      'WHITE_PATCHES_PIEBALD': '#A855F7',
      'MOTTLED_PIEBALD': '#8B5CF6',
      'SMALL_WHITE_SPOTS_PIEBALD': '#C084FC',
      'BLUE_SPREAD_LIGHT_PIEBALD': '#4B5563'
    };

    return colorMap[character] || '#6B7280';
  }

  getDistinctivenessColor(distinctiveness: string): string {
    switch (distinctiveness) {
      case 'common': return 'green';
      case 'unusual': return 'orange';
      case 'rare': return 'red';
      default: return 'gray';
    }
  }

  onImageError(event: any): void {
    event.target.src = 'assets/placeholder-pigeon.svg';
  }

  trackByFilename(index: number, pigeon: ClassifiedPigeon): string {
    return pigeon.filename;
  }

  onPigeonHover(pigeon: ClassifiedPigeon, event: MouseEvent): void {
    this.hoveredPigeon.set(pigeon);

    // Calculate panel position
    const panelWidth = 320; // w-80 = 320px
    const panelHeight = 400; // Approximate height
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const mouseX = viewportWidth - panelWidth;
    const mouseY = 0;

    // Position panel to the right of cursor, but adjust if it would go off screen
    let x = mouseX + 15;
    let y = mouseY - 50;

    // Adjust horizontal position if panel would go off right edge
    if (x + panelWidth > viewportWidth) {
      x = mouseX - panelWidth - 15;
    }

    // Adjust vertical position if panel would go off bottom edge
    if (y + panelHeight > viewportHeight) {
      y = viewportHeight - panelHeight - 10;
    }

    // Ensure panel doesn't go off top edge
    if (y < 10) {
      y = 10;
    }

    // Ensure panel doesn't go off left edge
    if (x < 10) {
      x = 10;
    }

    this.hoverPanelPosition.set({ x, y });
    this.showHoverPanel.set(true);
  }

  onPigeonLeave(): void {
    this.showHoverPanel.set(false);
    // Small delay before clearing the pigeon to allow for smooth transitions
    setTimeout(() => {
      if (!this.showHoverPanel()) {
        this.hoveredPigeon.set(null);
      }
    }, 100);
  }
}
